# 简化版多店铺订单导出工具使用说明

## 概述

已移除所有自动认证参数管理功能，改为手动配置方式。这样可以避免复杂的权限问题，用户完全控制认证参数的获取和使用。

## 主要变更

### 移除的功能
- ❌ 自动认证参数刷新
- ❌ 认证参数过期检测
- ❌ 自动重试机制
- ❌ 认证参数缓存
- ❌ TemuAuthManager类

### 保留的功能
- ✅ 多店铺订单导出
- ✅ 双区域支持（美国区/全球区）
- ✅ Excel文件生成
- ✅ 订单数据提取和格式化

## 使用步骤

### 1. 启动Chrome调试模式

运行启动脚本：
```
双击运行: 启动谷歌9229.bat
```

或手动启动：
```
chrome.exe --remote-debugging-port=9337 --user-data-dir="C:\chrome_debug_data_temu"
```

### 2. 登录Temu卖家后台

在Chrome浏览器中：
1. 访问 `https://seller.kuajingmaihuo.com`
2. 完成登录
3. 确保能正常访问店铺列表

### 3. 手动获取认证参数（重要）

由于移除了自动认证功能，需要手动获取最新的认证参数：

#### 方法1：使用开发者工具
1. 在Chrome中按F12打开开发者工具
2. 访问任一店铺的订单页面
3. 在Network标签中找到API请求
4. 复制Request Headers中的cookies
5. 更新config.json文件

#### 方法2：使用权限诊断工具
```bash
python 权限诊断工具.py
```
这会生成包含最新认证参数的文件。

### 4. 更新配置文件

编辑 `config.json` 文件，更新以下关键参数：
```json
{
    "cookies": {
        "AccessToken": "从浏览器复制的最新AccessToken",
        "seller_temp": "从浏览器复制的最新seller_temp",
        "user_uin": "用户标识",
        "mallid": "店铺ID"
    }
}
```

### 5. 运行导出工具

```bash
python 多店铺待发货订单导出工具.py
```

## 注意事项

### 认证参数有效期
- 认证参数会定期过期（通常30分钟-2小时）
- 当出现403错误时，需要重新获取认证参数
- 建议在每次使用前获取最新参数

### 权限问题
如果遇到"No Permission to Access"错误：
1. 确认账号有订单管理权限
2. 检查是否为主账号登录
3. 联系管理员申请相应权限

### 店铺访问
- 工具会自动获取账号下的所有店铺
- 支持美国区和全球区双区域
- 只导出有权限访问的店铺

## 故障排除

### 1. Chrome连接失败
```
❌ 连接浏览器失败
```
**解决方案**：
- 确保Chrome已启动调试模式
- 检查端口9337是否被占用
- 重新运行启动脚本

### 2. 认证失败
```
❌ 认证失败，状态码: 403
```
**解决方案**：
- 重新获取认证参数
- 更新config.json文件
- 确认登录状态

### 3. 无权限访问
```
❌ No Permission to Access
```
**解决方案**：
- 使用主账号登录
- 联系管理员申请权限
- 检查店铺权限设置

## 文件结构

```
双区订单导出/
├── 多店铺待发货订单导出工具.py    # 主程序（已简化）
├── 待发货订单导出工具.py          # 单店铺导出器
├── 订单数据导出工具.py            # 数据处理工具
├── config.json                   # 配置文件（需手动更新）
├── 启动谷歌9229.bat              # Chrome启动脚本
├── 权限诊断工具.py               # 权限检查工具
└── 多店铺待发货订单/             # 输出目录
```

## 优势

### 简单可靠
- 移除复杂的自动化逻辑
- 减少出错可能性
- 用户完全控制认证过程

### 透明度高
- 用户清楚知道使用的认证参数
- 便于调试和问题排查
- 避免黑盒操作

### 兼容性好
- 适用于各种权限级别的账号
- 不依赖特定的API权限
- 支持手动干预

## 使用建议

1. **定期更新认证参数**：建议每次使用前获取最新参数
2. **保存有效配置**：当找到有效的认证参数时，备份config.json
3. **分批处理**：如果店铺较多，可以分批处理避免超时
4. **监控输出**：注意观察控制台输出，及时发现问题

## 技术支持

如果遇到问题，请提供：
1. 完整的错误日志
2. config.json配置（隐藏敏感信息）
3. 账号权限级别
4. 使用的Chrome版本

这个简化版本更加稳定可靠，避免了复杂的权限和认证问题。
