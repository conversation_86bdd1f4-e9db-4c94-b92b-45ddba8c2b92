#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动获取Temu认证参数工具
用于获取最新的认证参数并更新配置文件
"""

import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options


def connect_to_chrome():
    """连接到Chrome调试端口"""
    try:
        print("🔗 连接到Chrome调试端口...")
        
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9337")
        
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Chrome连接成功")
        return driver
        
    except Exception as e:
        print(f"❌ Chrome连接失败: {e}")
        print("请确保Chrome已启动调试模式:")
        print("chrome.exe --remote-debugging-port=9337")
        return None


def get_cookies_from_browser(driver):
    """从浏览器获取cookies"""
    try:
        print("\n🍪 获取浏览器cookies...")
        
        # 访问主页面确保登录状态
        driver.get("https://seller.kuajingmaihuo.com/settle/site-main")
        time.sleep(3)
        
        current_url = driver.current_url
        if "login" in current_url.lower() or "authentication" in current_url.lower():
            print("❌ 需要先登录")
            print("请在浏览器中访问: https://seller.kuajingmaihuo.com")
            print("完成登录后重新运行此工具")
            return None
        
        # 获取所有cookies
        selenium_cookies = driver.get_cookies()
        cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
        
        print(f"✅ 获取到 {len(cookies_dict)} 个cookies")
        
        # 检查关键cookies
        required_cookies = ['AccessToken', 'seller_temp', 'user_uin']
        missing_cookies = [c for c in required_cookies if c not in cookies_dict]
        
        if missing_cookies:
            print(f"⚠️ 缺少关键cookies: {missing_cookies}")
            print("可能需要访问店铺页面来获取完整的认证参数")
            
            # 尝试访问一个店铺页面
            print("🔄 尝试访问店铺页面获取完整cookies...")
            driver.get("https://agentseller-us.temu.com/mmsos/orders.html")
            time.sleep(5)
            
            # 重新获取cookies
            selenium_cookies = driver.get_cookies()
            cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
            
            missing_cookies = [c for c in required_cookies if c not in cookies_dict]
            if missing_cookies:
                print(f"❌ 仍然缺少关键cookies: {missing_cookies}")
            else:
                print("✅ 已获取完整的认证参数")
        
        return cookies_dict
        
    except Exception as e:
        print(f"❌ 获取cookies失败: {e}")
        return None


def display_cookies_info(cookies_dict):
    """显示cookies信息"""
    print("\n📋 认证参数信息:")
    print("-" * 50)
    
    key_cookies = ['AccessToken', 'seller_temp', 'user_uin', 'mallid']
    
    for key in key_cookies:
        if key in cookies_dict:
            value = cookies_dict[key]
            if len(value) > 50:
                display_value = value[:30] + "..." + value[-10:]
            else:
                display_value = value
            print(f"{key}: {display_value}")
        else:
            print(f"{key}: ❌ 缺失")
    
    print(f"\n其他cookies数量: {len(cookies_dict) - len(key_cookies)}")


def update_config_file(cookies_dict):
    """更新配置文件"""
    try:
        print("\n💾 更新配置文件...")
        
        # 读取现有配置
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
        except FileNotFoundError:
            config = {"cookies": {}}
        
        # 更新cookies
        if 'cookies' not in config:
            config['cookies'] = {}
        
        # 更新关键认证参数
        key_cookies = ['AccessToken', 'seller_temp', 'user_uin', 'api_uid', 
                      'region', 'timezone', 'webp', '_nano_fp', '_bee', 
                      'njrpl', 'dilx', 'hfsc', 'isLogin', 'mallid']
        
        updated_count = 0
        for key in key_cookies:
            if key in cookies_dict:
                config['cookies'][key] = cookies_dict[key]
                updated_count += 1
        
        # 添加更新时间戳
        from datetime import datetime
        config['last_updated'] = datetime.now().isoformat()
        config['说明'] = "认证参数已自动更新"
        
        # 保存配置文件
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        
        print(f"✅ 已更新 {updated_count} 个认证参数")
        print("📁 配置文件已保存: config.json")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")
        return False


def test_auth_params(cookies_dict):
    """测试认证参数有效性"""
    print("\n🧪 测试认证参数有效性...")
    
    import requests
    
    # 测试店铺ID（可以从cookies中获取）
    test_mall_id = cookies_dict.get('mallid', '634418218659715')
    
    domains = [
        ("美国区", "https://agentseller-us.temu.com"),
        ("全球区", "https://agentseller.temu.com")
    ]
    
    success_count = 0
    
    for region_name, domain in domains:
        try:
            print(f"  测试 {region_name}...")
            
            headers = {
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "content-type": "application/json;charset=UTF-8",
                "mallid": str(test_mall_id),
                "origin": domain,
                "referer": f"{domain}/mmsos/orders.html",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            url = f"{domain}/kirogi/bg/mms/recentOrderList"
            data = {
                "fulfillmentMode": 0,
                "pageNumber": 1,
                "pageSize": 1,
                "queryType": 2,
                "sortType": 1,
                "timeZone": "UTC+8",
                "parentAfterSalesTag": 0,
                "needBuySignService": 0,
                "sellerNoteLabelList": []
            }
            
            response = requests.post(url, headers=headers, cookies=cookies_dict, 
                                   json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"    ✅ {region_name} 测试成功")
                    success_count += 1
                else:
                    print(f"    ❌ {region_name} API错误: {result.get('errorMsg', '未知错误')}")
            else:
                print(f"    ❌ {region_name} HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ {region_name} 测试失败: {e}")
    
    print(f"\n📊 测试结果: {success_count}/2 成功")
    return success_count > 0


def main():
    """主函数"""
    print("🔧 手动获取Temu认证参数工具")
    print("=" * 50)
    
    # 连接Chrome
    driver = connect_to_chrome()
    if not driver:
        return
    
    try:
        # 获取cookies
        cookies_dict = get_cookies_from_browser(driver)
        if not cookies_dict:
            return
        
        # 显示cookies信息
        display_cookies_info(cookies_dict)
        
        # 询问是否更新配置文件
        update_config = input("\n是否更新配置文件？(y/n): ").strip().lower()
        if update_config == 'y':
            if update_config_file(cookies_dict):
                print("✅ 配置文件更新成功")
            else:
                print("❌ 配置文件更新失败")
        
        # 询问是否测试认证参数
        test_auth = input("\n是否测试认证参数有效性？(y/n): ").strip().lower()
        if test_auth == 'y':
            if test_auth_params(cookies_dict):
                print("✅ 认证参数测试通过，可以运行导出工具")
            else:
                print("❌ 认证参数测试失败，可能需要检查权限")
        
        print("\n🎉 操作完成！")
        print("现在可以运行: python 多店铺待发货订单导出工具.py")
        
    finally:
        # 不关闭浏览器，只断开连接
        driver.quit()


if __name__ == "__main__":
    main()
