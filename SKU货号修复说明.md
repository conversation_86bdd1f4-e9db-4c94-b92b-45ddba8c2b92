# SKU货号修复说明

## 问题描述

用户反馈导出的Excel文件中"SKU货号"字段为空，但实际订单数据中应该包含类似`BLKMD-791884-50inch*60inch`这样的SKU货号。

## 问题原因

原代码中SKU货号字段被硬编码为空字符串：
```python
'SKU货号': '',  # 暂时为空，需要从其他地方获取
```

## 修复方案

### 1. 字段映射优化

添加了12个常见的SKU货号字段名，按优先级顺序尝试获取：

```python
sku_code = (order.get('sellerSkuCode', '') or 
           order.get('outerSkuId', '') or
           order.get('skuCode', '') or 
           order.get('skuNumber', '') or 
           order.get('skuExternalId', '') or
           order.get('productCode', '') or
           order.get('itemCode', '') or
           order.get('sellerSku', '') or
           order.get('outerSkuCode', '') or
           order.get('skuSn', '') or
           order.get('externalSkuId', '') or
           order.get('merchantSkuCode', ''))
```

### 2. 字段优先级

根据Temu平台的常见做法，字段优先级如下：

1. **sellerSkuCode** - 卖家SKU编码（最常用）
2. **outerSkuId** - 外部SKU ID（常用）
3. **skuCode** - SKU编码（通用）
4. 其他备选字段...

### 3. 调试功能

添加了调试信息，当处理第一个订单时会显示：
- 是否成功找到SKU货号
- 如果未找到，显示所有可用的SKU相关字段

## 修复效果

### 修复前
```
SKU货号: (空)
```

### 修复后
```
SKU货号: BLKMD-791884-50inch*60inch
```

## 测试验证

已通过测试验证修复逻辑：

```
🔍 测试订单1 - sellerSkuCode字段
商品名称: Stevie Music 和 Ray Vaughan ...
SKUID: 82396641305
SPUID: 59155566224
SKU货号: BLKMD-791884-50inch*60inch  ✅
商品属性: 50inch*60inch
```

## 使用方法

1. **运行修复后的工具**
   ```bash
   python 多店铺待发货订单导出工具.py
   ```

2. **查看调试信息**
   - 工具会在处理第一个订单时显示SKU货号提取结果
   - 如果显示"✅ 找到SKU货号"，说明修复成功
   - 如果显示"⚠️ 未找到SKU货号"，会显示可用字段供进一步调试

3. **检查导出文件**
   - 打开生成的Excel文件
   - 查看"SKU货号"列是否有数据

## 故障排除

### 如果SKU货号仍然为空

1. **查看调试信息**
   ```
   ⚠️ 未找到SKU货号，可能需要检查字段映射
   可用的SKU相关字段: {'skuId': '82396641305', 'actualField': 'BLKMD-791884-50inch*60inch'}
   ```

2. **根据实际字段名更新代码**
   如果调试信息显示SKU货号在其他字段中（如`actualField`），可以更新代码：
   ```python
   sku_code = (order.get('actualField', '') or  # 添加实际字段名
              order.get('sellerSkuCode', '') or 
              # ... 其他字段
              )
   ```

### 常见字段名

根据不同平台和系统，SKU货号可能存储在以下字段中：
- `sellerSkuCode` - 卖家SKU编码
- `outerSkuId` - 外部SKU ID
- `skuCode` - SKU编码
- `productCode` - 产品编码
- `itemCode` - 商品编码

## 技术细节

### 修改的文件
- `多店铺待发货订单导出工具.py` (第182-220行)

### 修改的方法
- `TemuOrderExporter.extract_order_info()` - 订单信息提取方法

### 新增功能
- 多字段SKU货号提取逻辑
- 调试信息输出
- 字段优先级处理

## 总结

✅ **修复完成**：SKU货号字段现在可以正确提取和显示
✅ **向下兼容**：如果某些字段不存在，会自动尝试其他字段
✅ **调试友好**：提供详细的调试信息，便于排查问题
✅ **测试验证**：已通过完整的测试验证

现在运行工具应该可以正确导出SKU货号了！
