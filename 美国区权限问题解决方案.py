#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美国区权限问题解决方案
专门处理美国区403错误，全球区正常的情况
"""

import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options


def get_region_specific_cookies(driver, mall_id):
    """获取特定区域的认证参数"""
    print(f"🔍 获取店铺 {mall_id} 的区域特定认证参数...")
    
    domains = [
        ("美国区", "https://agentseller-us.temu.com"),
        ("全球区", "https://agentseller.temu.com")
    ]
    
    region_cookies = {}
    
    for region_name, domain in domains:
        try:
            print(f"  获取 {region_name} 认证参数...")
            
            # 访问特定区域的店铺页面
            url = f"{domain}/mmsos/orders.html?init=true&mallId={mall_id}"
            driver.get(url)
            time.sleep(5)
            
            current_url = driver.current_url
            if "login" in current_url.lower() or "authentication" in current_url.lower():
                print(f"    ❌ {region_name} 需要登录")
                continue
            
            # 获取cookies
            selenium_cookies = driver.get_cookies()
            cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
            
            # 检查关键cookies
            required_cookies = ['AccessToken', 'seller_temp']
            if all(c in cookies_dict for c in required_cookies):
                region_cookies[region_name] = {
                    'domain': domain,
                    'cookies': cookies_dict
                }
                print(f"    ✅ {region_name} 认证参数获取成功")
            else:
                print(f"    ❌ {region_name} 缺少关键认证参数")
                
        except Exception as e:
            print(f"    ❌ {region_name} 获取失败: {e}")
    
    return region_cookies


def test_region_api_access(mall_id, region_cookies):
    """测试各区域的API访问权限"""
    print(f"\n🧪 测试店铺 {mall_id} 的API访问权限...")
    
    results = {}
    
    for region_name, region_data in region_cookies.items():
        domain = region_data['domain']
        cookies = region_data['cookies']
        
        print(f"  测试 {region_name}...")
        
        try:
            headers = {
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "content-type": "application/json;charset=UTF-8",
                "mallid": str(mall_id),
                "origin": domain,
                "referer": f"{domain}/mmsos/orders.html",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
            }
            
            url = f"{domain}/kirogi/bg/mms/recentOrderList"
            data = {
                "fulfillmentMode": 0,
                "pageNumber": 1,
                "pageSize": 1,
                "queryType": 2,
                "sortType": 1,
                "timeZone": "UTC+8",
                "parentAfterSalesTag": 0,
                "needBuySignService": 0,
                "sellerNoteLabelList": []
            }
            
            response = requests.post(url, headers=headers, cookies=cookies, 
                                   json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"    ✅ {region_name} API访问成功")
                    results[region_name] = {'success': True, 'cookies': cookies, 'domain': domain}
                else:
                    error_msg = result.get('errorMsg', '未知错误')
                    print(f"    ❌ {region_name} API错误: {error_msg}")
                    results[region_name] = {'success': False, 'error': error_msg}
            else:
                print(f"    ❌ {region_name} HTTP {response.status_code}")
                if response.status_code == 403:
                    try:
                        error_data = response.json()
                        error_msg = error_data.get('error_msg', 'Permission denied')
                        print(f"      错误详情: {error_msg}")
                    except:
                        print(f"      响应内容: {response.text[:100]}")
                results[region_name] = {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"    ❌ {region_name} 测试异常: {e}")
            results[region_name] = {'success': False, 'error': str(e)}
    
    return results


def generate_region_specific_config(test_results):
    """生成区域特定的配置"""
    print("\n📝 生成区域特定配置...")
    
    config = {
        "说明": "区域特定认证配置 - 根据测试结果自动生成",
        "regions": {}
    }
    
    for region_name, result in test_results.items():
        if result['success']:
            config["regions"][region_name] = {
                "domain": result['domain'],
                "cookies": result['cookies'],
                "status": "可用"
            }
            print(f"  ✅ {region_name}: 配置已添加")
        else:
            config["regions"][region_name] = {
                "status": "不可用",
                "error": result['error']
            }
            print(f"  ❌ {region_name}: {result['error']}")
    
    # 保存配置
    try:
        with open('区域特定配置.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        print("💾 配置已保存到: 区域特定配置.json")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False


def analyze_permission_differences():
    """分析权限差异"""
    print("\n🔍 分析美国区和全球区权限差异...")
    
    print("根据测试结果分析:")
    print("1. 全球区 (agentseller.temu.com) - ✅ 可以正常访问")
    print("2. 美国区 (agentseller-us.temu.com) - ❌ 返回403错误")
    
    print("\n可能的原因:")
    print("• 美国区需要特殊的权限或认证")
    print("• 账号可能只有全球区的访问权限")
    print("• 美国区可能需要不同的认证参数")
    print("• 地理位置限制或IP白名单")
    
    print("\n建议的解决方案:")
    print("1. 只使用全球区进行订单导出")
    print("2. 联系管理员申请美国区权限")
    print("3. 检查账号的区域权限设置")
    print("4. 使用不同的认证参数访问美国区")


def create_global_only_config():
    """创建仅全球区的配置"""
    print("\n🌍 创建仅全球区配置...")
    
    try:
        # 读取现有的区域配置
        with open('区域特定配置.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查全球区是否可用
        global_region = config.get('regions', {}).get('全球区', {})
        if global_region.get('status') == '可用':
            # 创建仅全球区的简化配置
            simple_config = {
                "说明": "仅全球区配置 - 美国区权限不足，只使用全球区",
                "cookies": global_region['cookies'],
                "domain": global_region['domain'],
                "region": "全球区"
            }
            
            with open('仅全球区配置.json', 'w', encoding='utf-8') as f:
                json.dump(simple_config, f, indent=4, ensure_ascii=False)
            
            print("✅ 仅全球区配置已创建: 仅全球区配置.json")
            print("💡 建议修改多店铺导出工具，只使用全球区域名")
            return True
        else:
            print("❌ 全球区配置不可用，无法创建简化配置")
            return False
            
    except Exception as e:
        print(f"❌ 创建配置失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 美国区权限问题解决方案")
    print("=" * 50)
    
    # 连接Chrome
    try:
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9337")
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Chrome连接成功")
    except Exception as e:
        print(f"❌ Chrome连接失败: {e}")
        return
    
    try:
        # 使用一个测试店铺ID
        test_mall_id = "634418218770953"  # 从日志中选择一个全球区成功的店铺
        
        # 获取区域特定的认证参数
        region_cookies = get_region_specific_cookies(driver, test_mall_id)
        
        if not region_cookies:
            print("❌ 无法获取任何区域的认证参数")
            return
        
        # 测试API访问权限
        test_results = test_region_api_access(test_mall_id, region_cookies)
        
        # 生成配置文件
        if generate_region_specific_config(test_results):
            # 分析权限差异
            analyze_permission_differences()
            
            # 创建仅全球区配置
            create_global_only_config()
        
        print("\n🎯 解决方案总结:")
        print("1. 已生成区域特定配置文件")
        print("2. 建议只使用全球区进行订单导出")
        print("3. 如需美国区权限，请联系管理员")
        
    finally:
        driver.quit()


if __name__ == "__main__":
    main()
