#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双区域Temu订单导出测试脚本
测试美国区和全球区的订单导出功能
"""

import time
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from 认证参数管理器 import TemuAuthManager
from 待发货订单导出工具 import TemuOrderExporter


def test_dual_region_export():
    """测试双区域订单导出"""
    print("🚀 双区域Temu订单导出测试")
    print("=" * 50)
    
    # 连接Chrome浏览器
    print("🌐 正在连接Chrome浏览器...")
    chrome_options = webdriver.ChromeOptions()
    chrome_options.debugger_address = "127.0.0.1:9337"
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        wait = WebDriverWait(driver, 10)
        auth_manager = TemuAuthManager(driver)
        print("✅ 浏览器连接成功")
    except Exception as e:
        print(f"❌ 连接浏览器失败: {e}")
        return
    
    # 测试店铺ID
    test_mall_id = "634418218259845"
    
    # 定义区域信息
    regions = [
        {"domain": "https://agentseller-us.temu.com", "name": "美国区"},
        {"domain": "https://agentseller.temu.com", "name": "全球区"}
    ]
    
    results = {}
    
    for region in regions:
        domain = region["domain"]
        region_name = region["name"]
        
        print(f"\n🌍 测试 {region_name} ({domain})")
        print("-" * 40)
        
        try:
            # 获取认证参数
            print(f"🔑 获取 {region_name} 认证参数...")
            cookies = auth_manager.get_valid_cookies(test_mall_id, domain=domain)
            
            if not cookies:
                print(f"❌ 无法获取 {region_name} 认证参数")
                results[region_name] = {"success": False, "error": "认证参数获取失败"}
                continue
            
            # 测试认证参数有效性
            print(f"🧪 测试 {region_name} 认证参数有效性...")
            is_valid = auth_manager.test_auth_validity(test_mall_id, cookies, domain)
            
            if not is_valid:
                print(f"❌ {region_name} 认证参数无效")
                results[region_name] = {"success": False, "error": "认证参数无效"}
                continue
            
            print(f"✅ {region_name} 认证参数有效")
            
            # 创建导出器并测试订单获取
            print(f"📦 测试 {region_name} 订单获取...")
            access_token = cookies.get('AccessToken', '')
            exporter = TemuOrderExporter(test_mall_id, access_token, cookies, domain)
            
            # 获取待发货订单（限制为1页进行测试）
            pending_orders = exporter.get_pending_orders(max_pages=1)
            
            print(f"✅ {region_name} 成功获取 {len(pending_orders)} 个待发货订单")
            
            results[region_name] = {
                "success": True,
                "order_count": len(pending_orders),
                "domain": domain,
                "auth_valid": True
            }
            
        except Exception as e:
            error_msg = str(e)
            print(f"❌ {region_name} 测试失败: {error_msg}")
            results[region_name] = {"success": False, "error": error_msg}
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("🎉 双区域测试结果汇总")
    print("=" * 50)
    
    for region_name, result in results.items():
        status = "✅" if result["success"] else "❌"
        if result["success"]:
            print(f"{status} {region_name}: {result['order_count']} 个订单")
            print(f"   域名: {result['domain']}")
        else:
            print(f"{status} {region_name}: 失败 - {result['error']}")
    
    # 检查是否两个区域都成功
    success_count = sum(1 for r in results.values() if r["success"])
    total_orders = sum(r.get("order_count", 0) for r in results.values() if r["success"])
    
    print(f"\n📊 总体统计:")
    print(f"   成功区域: {success_count}/2")
    print(f"   总订单数: {total_orders}")
    
    if success_count == 2:
        print("🎉 双区域导出功能测试通过！")
    elif success_count == 1:
        print("⚠️ 部分区域可用，请检查失败的区域")
    else:
        print("❌ 双区域导出功能测试失败")
    
    # 询问是否关闭浏览器
    try:
        close_browser = input("\n是否关闭浏览器？(y/n): ").strip().lower()
        if close_browser == 'y':
            driver.quit()
            print("🔒 浏览器已关闭")
    except:
        pass


def test_region_differences():
    """测试不同区域的API差异"""
    print("\n🔍 测试区域API差异")
    print("-" * 30)
    
    regions = [
        {"domain": "https://agentseller-us.temu.com", "name": "美国区"},
        {"domain": "https://agentseller.temu.com", "name": "全球区"}
    ]
    
    for region in regions:
        domain = region["domain"]
        region_name = region["name"]
        api_url = f"{domain}/kirogi/bg/mms/recentOrderList"
        
        print(f"🌍 {region_name}:")
        print(f"   域名: {domain}")
        print(f"   API: {api_url}")
        print(f"   Referer: {domain}/mmsos/orders.html")
        print()


if __name__ == "__main__":
    test_dual_region_export()
    test_region_differences()
