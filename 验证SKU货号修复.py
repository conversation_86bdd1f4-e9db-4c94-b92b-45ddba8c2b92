#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证SKU货号修复
测试extCodeList字段的SKU货号提取逻辑
"""

def test_ext_code_list_extraction():
    """测试extCodeList字段的SKU货号提取"""
    print("🧪 测试extCodeList字段的SKU货号提取")
    print("=" * 60)
    
    # 模拟不同格式的extCodeList数据
    test_cases = [
        {
            "name": "标准格式 - 字符串列表",
            "order": {
                "goodsName": "Stevie Music 和 Ray Vaughan ...",
                "skuId": "82396641305",
                "goodsId": "59155566224",
                "extCodeList": ["BLKMD-791884-50inch*60inch"],
                "spec": "50inch*60inch"
            },
            "expected": "BLKMD-791884-50inch*60inch"
        },
        {
            "name": "多个SKU码",
            "order": {
                "goodsName": "多规格商品",
                "skuId": "12345678901",
                "goodsId": "98765432109",
                "extCodeList": ["SKU-001", "SKU-002", "SKU-003"],
                "spec": "多规格"
            },
            "expected": "SKU-001"  # 取第一个
        },
        {
            "name": "空列表",
            "order": {
                "goodsName": "无SKU货号商品",
                "skuId": "11111111111",
                "goodsId": "22222222222",
                "extCodeList": [],
                "spec": "基本规格"
            },
            "expected": ""
        },
        {
            "name": "字段不存在",
            "order": {
                "goodsName": "缺少extCodeList字段",
                "skuId": "33333333333",
                "goodsId": "44444444444",
                "spec": "无字段"
            },
            "expected": ""
        },
        {
            "name": "非列表格式",
            "order": {
                "goodsName": "非标准格式",
                "skuId": "55555555555",
                "goodsId": "66666666666",
                "extCodeList": "SINGLE-SKU-CODE",
                "spec": "单个字符串"
            },
            "expected": "SINGLE-SKU-CODE"
        }
    ]
    
    # 测试每个案例
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 测试案例 {i}: {test_case['name']}")
        print("-" * 50)
        
        order = test_case['order']
        expected = test_case['expected']
        
        # 使用与主程序相同的逻辑
        sku_code = ''
        ext_code_list = order.get('extCodeList', [])
        if ext_code_list and len(ext_code_list) > 0:
            sku_code = ext_code_list[0] if isinstance(ext_code_list, list) else str(ext_code_list)
        
        print(f"extCodeList: {ext_code_list}")
        print(f"提取的SKU货号: '{sku_code}'")
        print(f"期望的SKU货号: '{expected}'")
        
        if sku_code == expected:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
    
    print("\n" + "=" * 60)


def show_implementation_details():
    """显示实现细节"""
    print("📋 SKU货号提取实现细节")
    print("=" * 60)
    
    print("🔍 字段名: extCodeList")
    print("📝 数据类型: 通常是列表（list）")
    print("🎯 提取逻辑:")
    print("   1. 获取 order.get('extCodeList', [])")
    print("   2. 检查列表是否非空")
    print("   3. 如果是列表，取第一个元素")
    print("   4. 如果不是列表，转换为字符串")
    
    print("\n💻 代码实现:")
    code = '''
# 提取SKU货号 - 从extCodeList字段获取
sku_code = ''
ext_code_list = order.get('extCodeList', [])
if ext_code_list and len(ext_code_list) > 0:
    # extCodeList通常是一个列表，取第一个元素作为SKU货号
    sku_code = ext_code_list[0] if isinstance(ext_code_list, list) else str(ext_code_list)
'''
    print(code)


def create_usage_guide():
    """创建使用指南"""
    print("🚀 使用指南")
    print("=" * 60)
    
    print("1. 运行修复后的工具:")
    print("   python 多店铺待发货订单导出工具.py")
    
    print("\n2. 查看调试信息:")
    print("   工具会显示第一个订单的extCodeList内容")
    print("   例如: 🔍 调试信息 - extCodeList: ['BLKMD-791884-50inch*60inch']")
    print("        ✅ 成功提取SKU货号: BLKMD-791884-50inch*60inch")
    
    print("\n3. 检查导出结果:")
    print("   打开生成的Excel文件")
    print("   查看'SKU货号'列是否有正确的数据")
    
    print("\n4. 故障排除:")
    print("   如果显示'⚠️ extCodeList为空或无效'")
    print("   说明该字段可能不存在或格式不同")
    print("   需要进一步检查订单数据结构")


def main():
    """主函数"""
    test_ext_code_list_extraction()
    show_implementation_details()
    create_usage_guide()
    
    print("\n🎉 SKU货号修复验证完成！")
    print("现在可以运行主程序测试实际效果。")


if __name__ == "__main__":
    main()
