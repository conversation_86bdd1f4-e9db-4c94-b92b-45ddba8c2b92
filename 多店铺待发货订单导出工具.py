#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多店铺Temu待发货订单导出工具
结合auto_getOrders_multistore_final.py的多店铺获取功能
和待发货订单导出工具的精确筛选功能
"""

import json
import requests
import pandas as pd
import time
import os
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from 待发货订单导出工具 import TemuOrderExporter
from 订单数据导出工具 import OrderDataExporter


class MultiStoreOrderExporter:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def get_mall_ids(self, response_json):
        """从用户信息API响应中提取店铺ID列表"""
        if not response_json.get('success'):
            print("接口返回失败")
            return []
        
        mall_ids = []
        company_list = response_json['result'].get('companyList', [])
        
        for company in company_list:
            mal_info_list = company.get('malInfoList', [])
            for mall in mal_info_list:
                mall_ids.append(mall['mallId'])
        
        return mall_ids

    def get_all_store_ids(self):
        """获取所有店铺的ID"""
        print("🔍 正在获取所有店铺ID...")
        try:
            print("🌐 从seller.kuajingmaihuo.com获取店铺列表...")
            self.driver.get("https://seller.kuajingmaihuo.com/settle/site-main")
            time.sleep(5)
            
            selenium_cookies = self.driver.get_cookies()
            cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
            anti_content = self.driver.execute_script("return window.__ANTI__ || '';")
            
            headers = {
                "accept": "*/*",
                "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
                "anti-content": anti_content,
                "cache-control": "max-age=0",
                "content-type": "application/json",
                "origin": "https://seller.kuajingmaihuo.com",
                "priority": "u=1, i",
                "referer": "https://seller.kuajingmaihuo.com/settle/site-main",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
            
            url = "https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo"
            response = requests.post(url, headers=headers, cookies=cookies_dict, json={})
            response_json = response.json()
            
            # 获取店铺ID列表
            mall_ids = self.get_mall_ids(response_json)
            print(f"✅ 从seller.kuajingmaihuo.com获取到店铺ID: {mall_ids}")
            
            if not mall_ids:
                print("❌ 未获取到店铺ID列表")
                return []
            
            # 测试店铺在Temu系统中的可用性（美国区和全球区）
            print("🔄 测试店铺在Temu系统中的可用性...")
            available_stores = []

            # 定义区域信息 - 根据权限测试结果，优先使用全球区
            regions = [
                {"domain": "https://agentseller.temu.com", "name": "全球区"},
                {"domain": "https://agentseller-us.temu.com", "name": "美国区"}
            ]

            for mall_id in mall_ids:
                for region in regions:
                    domain = region["domain"]
                    region_name = region["name"]

                    try:
                        test_url = f"{domain}/mmsos/orders.html?init=true&mallId={mall_id}"
                        print(f"   🔍 测试店铺: {mall_id} 在 {region_name} ({domain})")

                        self.driver.get(test_url)
                        time.sleep(3)

                        current_url = self.driver.current_url
                        if "login" in current_url.lower() or "error" in current_url.lower():
                            print(f"      ❌ {region_name} 需要登录或出错")
                            continue

                        # 获取cookies用于API调用
                        selenium_cookies = self.driver.get_cookies()
                        cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}

                        store_info = {
                            'mall_id': mall_id,
                            'mall_name': f'店铺_{mall_id}_{region_name}',
                            'region_name': region_name,
                            'domain': domain,
                            'cookies': cookies_dict
                        }
                        available_stores.append(store_info)
                        print(f"      ✅ 店铺 {mall_id} 在 {region_name} 可用")

                    except Exception as e:
                        print(f"      ❌ 测试店铺 {mall_id} 在 {region_name} 失败: {e}")
                        continue
            
            print(f"✅ 最终发现 {len(available_stores)} 个可用店铺")
            return available_stores
            
        except Exception as e:
            print(f"❌ 获取店铺ID失败: {e}")
            return []

    # 移除自动刷新认证参数功能

    def quick_test_region_access(self, mall_id, cookies, domain):
        """快速测试区域访问权限"""
        try:
            import requests

            headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json;charset=UTF-8",
                "mallid": str(mall_id),
                "origin": domain,
                "referer": f"{domain}/mmsos/orders.html"
            }

            url = f"{domain}/kirogi/bg/mms/recentOrderList"
            data = {
                "fulfillmentMode": 0,
                "pageNumber": 1,
                "pageSize": 1,
                "queryType": 2,
                "sortType": 1,
                "timeZone": "UTC+8",
                "parentAfterSalesTag": 0,
                "needBuySignService": 0,
                "sellerNoteLabelList": []
            }

            response = requests.post(url, headers=headers, cookies=cookies,
                                   json=data, timeout=5)

            return response.status_code == 200 and response.json().get('success', False)

        except:
            return False

    def export_store_orders(self, store_info):
        """导出单个店铺的待发货订单"""
        mall_id = store_info['mall_id']
        mall_name = store_info['mall_name']
        cookies = store_info['cookies']
        domain = store_info.get('domain', 'https://agentseller.temu.com')
        region_name = store_info.get('region_name', '未知区域')

        print(f"\n🏪 开始处理店铺: {mall_name} (ID: {mall_id}) - {region_name}")

        # 快速测试区域访问权限
        if not self.quick_test_region_access(mall_id, cookies, domain):
            print(f"   ⚠️ 店铺 {mall_name} 在 {region_name} 无访问权限，跳过")
            return {
                'mall_id': mall_id,
                'mall_name': mall_name,
                'region_name': region_name,
                'success': False,
                'error': f'{region_name}无访问权限',
                'order_count': 0,
                'files': []
            }

        try:
            # 直接使用传入的cookies，不进行自动刷新
            access_token = cookies.get('AccessToken', '')
            domain = store_info.get('domain', 'https://agentseller-us.temu.com')
            exporter = TemuOrderExporter(mall_id, access_token, cookies, domain)

            # 获取区域名称
            region_name = store_info.get('region_name', '未知区域')

            # 获取待发货订单
            pending_orders = exporter.get_pending_orders(max_pages=10)

            if not pending_orders:
                print(f"   📋 店铺 {mall_name} 没有待发货订单")
                return {
                    'mall_id': mall_id,
                    'mall_name': mall_name,
                    'region_name': region_name,
                    'success': True,
                    'order_count': 0,
                    'files': []
                }

            # 提取订单信息
            order_data = exporter.extract_order_info(pending_orders)

            # 为每个订单添加店铺和区域信息
            for order in order_data:
                order['店铺ID'] = mall_id
                order['店铺名称'] = mall_name
                order['区域'] = region_name
                order['域名'] = store_info.get('domain', '')

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_filename = f"多店铺待发货订单/{mall_name}_待发货订单_{timestamp}.xlsx"

            # 导出文件
            files = []
            excel_file = exporter.export_to_excel(order_data, excel_filename)
            if excel_file:
                files.append(excel_file)

            print(f"   ✅ 店铺 {mall_name} 完成，导出 {len(order_data)} 个待发货订单")

            return {
                'mall_id': mall_id,
                'mall_name': mall_name,
                'region_name': region_name,
                'success': True,
                'order_count': len(order_data),
                'files': files,
                'orders': order_data
            }

        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ 店铺 {mall_name} 导出失败: {error_msg}")
            return {
                'mall_id': mall_id,
                'mall_name': mall_name,
                'region_name': store_info.get('region_name', '未知区域'),
                'success': False,
                'error': error_msg,
                'order_count': 0,
                'files': []
            }

    def export_all_stores(self, stores, max_workers=3):
        """并发导出所有店铺的待发货订单"""
        print(f"\n🏭 开始并发处理 {len(stores)} 个店铺的待发货订单...")
        
        # 创建输出目录
        os.makedirs("多店铺待发货订单", exist_ok=True)
        
        all_results = []
        all_orders = []
        
        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_store = {
                executor.submit(self.export_store_orders, store): store 
                for store in stores
            }
            
            for future in as_completed(future_to_store):
                store = future_to_store[future]
                try:
                    result = future.result()
                    all_results.append(result)
                    
                    if result['success'] and 'orders' in result:
                        all_orders.extend(result['orders'])
                        
                except Exception as e:
                    print(f"❌ 处理店铺 {store['mall_name']} 时发生异常: {e}")
                    all_results.append({
                        'mall_id': store['mall_id'],
                        'mall_name': store['mall_name'],
                        'success': False,
                        'error': str(e),
                        'order_count': 0,
                        'files': []
                    })
        
        return all_results, all_orders

    def generate_summary_report(self, results, all_orders):
        """生成汇总报告"""
        print("\n" + "="*80)
        print("🎉 多店铺待发货订单导出完成!")
        print("="*80)
        
        # 统计信息
        total_stores = len(results)
        successful_stores = len([r for r in results if r['success']])
        total_orders = sum(r['order_count'] for r in results if r['success'])
        
        print(f"📊 导出统计:")
        print(f"   处理店铺数: {total_stores}")
        print(f"   成功店铺数: {successful_stores}")
        print(f"   失败店铺数: {total_stores - successful_stores}")
        print(f"   待发货订单总数: {total_orders}")
        
        # 按店铺和区域显示详情
        print(f"\n📋 各店铺详情:")

        # 按区域分组统计
        region_stats = {}
        for result in results:
            region_name = result.get('region_name', '未知区域')
            if region_name not in region_stats:
                region_stats[region_name] = {'success': 0, 'failed': 0, 'orders': 0}

            if result['success']:
                region_stats[region_name]['success'] += 1
                region_stats[region_name]['orders'] += result['order_count']
            else:
                region_stats[region_name]['failed'] += 1

        # 显示区域统计
        print(f"\n📊 区域统计:")
        for region_name, stats in region_stats.items():
            total_stores = stats['success'] + stats['failed']
            print(f"   🌍 {region_name}: {total_stores} 个店铺, {stats['orders']} 个订单")

        # 显示详细店铺信息
        for result in results:
            status = "✅" if result['success'] else "❌"
            region_name = result.get('region_name', '未知区域')
            if result['success']:
                print(f"   {status} {result['mall_name']} ({region_name}): {result['order_count']} 个订单")
            else:
                print(f"   {status} {result['mall_name']} ({region_name}): 失败 - {result.get('error', '未知错误')}")
        
        # 生成汇总文件
        if all_orders:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 汇总Excel文件
            summary_excel = f"多店铺待发货订单/汇总_所有店铺待发货订单_{timestamp}.xlsx"
            df = pd.DataFrame(all_orders)
            
            # 重新排列列顺序，把店铺和区域信息放在前面
            cols = df.columns.tolist()
            priority_cols = ['店铺ID', '店铺名称', '区域', '域名']
            remaining_cols = [col for col in cols if col not in priority_cols]
            existing_priority_cols = [col for col in priority_cols if col in cols]
            df = df[existing_priority_cols + remaining_cols]
            
            # 使用新的订单数据导出工具，保持与订单导出2025719002.xlsx一致的格式
            order_exporter = OrderDataExporter()

            # 准备多店铺数据
            store_orders_dict = {}
            for result in results:
                if result['success'] and result['order_count'] > 0:
                    store_info = {
                        'store_id': result['mall_id'],
                        'store_name': result['mall_name']
                    }
                    store_orders = [order for order in all_orders if order['店铺ID'] == result['mall_id']]
                    if store_orders:
                        store_orders_dict[result['mall_name']] = store_orders

            # 使用订单数据导出工具导出
            export_result = order_exporter.export_multi_store_orders(
                store_orders_dict=store_orders_dict,
                output_dir="多店铺待发货订单",
                create_summary=True
            )

            if export_result['success'] and export_result['summary_files']:
                print(f"\n📄 汇总文件已生成:")
                for file in export_result['summary_files']:
                    print(f"   📄 {file}")
            else:
                # 如果新工具失败，回退到原来的方法
                with pd.ExcelWriter(summary_excel, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='所有店铺待发货订单', index=False)

                    # 按店铺分别创建工作表
                    for result in results:
                        if result['success'] and result['order_count'] > 0:
                            store_orders = [order for order in all_orders if order['店铺ID'] == result['mall_id']]
                            if store_orders:
                                store_df = pd.DataFrame(store_orders)
                                sheet_name = f"{result['mall_name']}"[:31]  # Excel工作表名称限制
                                store_df.to_excel(writer, sheet_name=sheet_name, index=False)

                    # 自动调整列宽
                    for sheet_name in writer.sheets:
                        worksheet = writer.sheets[sheet_name]
                        for column in worksheet.columns:
                            max_length = 0
                            column_letter = column[0].column_letter
                            for cell in column:
                                try:
                                    if len(str(cell.value)) > max_length:
                                        max_length = len(str(cell.value))
                                except:
                                    pass
                            adjusted_width = min(max_length + 2, 50)
                            worksheet.column_dimensions[column_letter].width = adjusted_width

                print(f"\n📄 汇总文件已生成: {summary_excel}")
            
            print(f"\n📄 汇总文件已生成: {summary_excel}")
        
        # 紧急订单统计
        if all_orders:
            urgent_orders = []
            for order in all_orders:
                remaining_time = order.get('剩余发货时间', '')
                if '小时' in remaining_time or remaining_time == '已超时':
                    urgent_orders.append(order)
            
            if urgent_orders:
                print(f"\n⚠️ 紧急订单提醒: {len(urgent_orders)} 个订单需要在24小时内发货或已超时")
                print("   紧急订单店铺分布:")
                urgent_by_store = {}
                for order in urgent_orders:
                    store_name = order.get('店铺名称', '未知')
                    urgent_by_store[store_name] = urgent_by_store.get(store_name, 0) + 1
                
                for store_name, count in urgent_by_store.items():
                    print(f"     {store_name}: {count} 个紧急订单")
        
        print(f"\n📁 输出目录: {os.path.abspath('多店铺待发货订单')}")
        print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    def run(self):
        """主运行函数"""
        print("🚀 多店铺Temu待发货订单导出工具 (双区域版)")
        print("=" * 70)
        print("自动获取所有店铺并导出待发货订单")
        print("支持美国区 (agentseller-us.temu.com) 和全球区 (agentseller.temu.com)")
        print("=" * 70)
        
        # 连接Chrome浏览器
        print("🌐 正在连接Chrome浏览器...")
        chrome_options = webdriver.ChromeOptions()
        chrome_options.debugger_address = "127.0.0.1:9337"
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            print("✅ 浏览器连接成功")
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            print("💡 请确保Chrome浏览器已启动调试模式:")
            print("   chrome.exe --remote-debugging-port=9337")
            return
        
        try:
            # 获取所有店铺
            stores = self.get_all_store_ids()
            
            if not stores:
                print("❌ 未发现任何可用店铺")
                return
            
            print(f"\n📋 发现店铺列表 ({len(stores)}个):")
            for i, store in enumerate(stores, 1):
                print(f"   {i}. {store['mall_name']} (ID: {store['mall_id']})")
            
            # 确认开始处理
            confirm = input(f"\n是否开始导出所有店铺的待发货订单？(y/n): ").strip().lower()
            if confirm != 'y':
                print("❌ 用户取消操作")
                return
            
            # 开始导出
            results, all_orders = self.export_all_stores(stores, max_workers=3)
            
            # 生成汇总报告
            self.generate_summary_report(results, all_orders)
            
        finally:
            # 询问是否关闭浏览器
            try:
                close_browser = input("\n是否关闭浏览器？(y/n): ").strip().lower()
                if close_browser == 'y':
                    self.driver.quit()
                    print("🔒 浏览器已关闭")
            except:
                pass


def main():
    """主函数"""
    exporter = MultiStoreOrderExporter()
    exporter.run()


if __name__ == "__main__":
    main()
