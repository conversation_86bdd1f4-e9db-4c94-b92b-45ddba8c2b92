#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复403错误和变量作用域错误的脚本
"""

import json
import os
from datetime import datetime


def create_fixed_multi_store_exporter():
    """创建修复后的多店铺导出工具"""
    
    fixed_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多店铺Temu待发货订单导出工具 - 修复版
修复了变量作用域错误和403权限问题
"""

import json
import requests
import pandas as pd
import time
import os
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from 待发货订单导出工具 import TemuOrderExporter
from 订单数据导出工具 import OrderDataExporter


class MultiStoreOrderExporter:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def get_mall_ids(self, response_json):
        """从用户信息API响应中提取店铺ID列表"""
        if not response_json.get('success'):
            print("接口返回失败")
            return []
        
        mall_ids = []
        company_list = response_json['result'].get('companyList', [])
        
        for company in company_list:
            mal_info_list = company.get('malInfoList', [])
            for mall in mal_info_list:
                mall_ids.append(mall['mallId'])
        
        return mall_ids

    def get_all_store_ids(self):
        """获取所有店铺的ID"""
        print("🔍 正在获取所有店铺ID...")
        try:
            print("🌐 从seller.kuajingmaihuo.com获取店铺列表...")
            self.driver.get("https://seller.kuajingmaihuo.com/settle/site-main")
            time.sleep(5)
            
            selenium_cookies = self.driver.get_cookies()
            cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
            anti_content = self.driver.execute_script("return window.__ANTI__ || '';")
            
            headers = {
                "accept": "*/*",
                "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
                "anti-content": anti_content,
                "cache-control": "max-age=0",
                "content-type": "application/json",
                "origin": "https://seller.kuajingmaihuo.com",
                "priority": "u=1, i",
                "referer": "https://seller.kuajingmaihuo.com/settle/site-main",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
            
            url = "https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo"
            response = requests.post(url, headers=headers, cookies=cookies_dict, json={})
            response_json = response.json()
            
            # 获取店铺ID列表
            mall_ids = self.get_mall_ids(response_json)
            print(f"✅ 从seller.kuajingmaihuo.com获取到店铺ID: {mall_ids}")
            
            if not mall_ids:
                print("❌ 未获取到店铺ID列表")
                return []
            
            # 只测试全球区，因为美国区有权限问题
            print("🔄 测试店铺在全球区的可用性...")
            available_stores = []

            for mall_id in mall_ids:
                try:
                    domain = "https://agentseller.temu.com"
                    region_name = "全球区"
                    
                    test_url = f"{domain}/mmsos/orders.html?init=true&mallId={mall_id}"
                    print(f"   🔍 测试店铺: {mall_id} 在 {region_name}")

                    self.driver.get(test_url)
                    time.sleep(3)

                    current_url = self.driver.current_url
                    if "login" in current_url.lower() or "error" in current_url.lower():
                        print(f"      ❌ {region_name} 需要登录或出错")
                        continue

                    # 获取cookies用于API调用
                    selenium_cookies = self.driver.get_cookies()
                    cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}

                    store_info = {
                        'mall_id': mall_id,
                        'mall_name': f'店铺_{mall_id}_{region_name}',
                        'region_name': region_name,
                        'domain': domain,
                        'cookies': cookies_dict
                    }
                    available_stores.append(store_info)
                    print(f"      ✅ 店铺 {mall_id} 在 {region_name} 可用")

                except Exception as e:
                    print(f"      ❌ 测试店铺 {mall_id} 失败: {e}")
                    continue
            
            print(f"✅ 最终发现 {len(available_stores)} 个可用店铺（仅全球区）")
            return available_stores
            
        except Exception as e:
            print(f"❌ 获取店铺ID失败: {e}")
            return []

    def export_store_orders(self, store_info):
        """导出单个店铺的待发货订单"""
        mall_id = store_info['mall_id']
        mall_name = store_info['mall_name']
        cookies = store_info['cookies']
        domain = store_info.get('domain', 'https://agentseller.temu.com')
        region_name = store_info.get('region_name', '全球区')

        print(f"\\n🏪 开始处理店铺: {mall_name} (ID: {mall_id})")

        try:
            # 直接使用传入的cookies
            access_token = cookies.get('AccessToken', '')
            exporter = TemuOrderExporter(mall_id, access_token, cookies, domain)

            # 获取待发货订单
            pending_orders = exporter.get_pending_orders(max_pages=10)

            if not pending_orders:
                print(f"   📋 店铺 {mall_name} 没有待发货订单")
                return {
                    'mall_id': mall_id,
                    'mall_name': mall_name,
                    'region_name': region_name,
                    'success': True,
                    'order_count': 0,
                    'files': []
                }

            # 提取订单信息
            order_data = exporter.extract_order_info(pending_orders)

            # 为每个订单添加店铺和区域信息
            for order in order_data:
                order['店铺ID'] = mall_id
                order['店铺名称'] = mall_name
                order['区域'] = region_name
                order['域名'] = domain

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_filename = f"多店铺待发货订单/{mall_name}_待发货订单_{timestamp}.xlsx"

            # 导出文件
            files = []
            excel_file = exporter.export_to_excel(order_data, excel_filename)
            if excel_file:
                files.append(excel_file)

            print(f"   ✅ 店铺 {mall_name} 完成，导出 {len(order_data)} 个待发货订单")

            return {
                'mall_id': mall_id,
                'mall_name': mall_name,
                'region_name': region_name,
                'success': True,
                'order_count': len(order_data),
                'files': files,
                'orders': order_data
            }

        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ 店铺 {mall_name} 导出失败: {error_msg}")
            return {
                'mall_id': mall_id,
                'mall_name': mall_name,
                'region_name': region_name,
                'success': False,
                'error': error_msg,
                'order_count': 0,
                'files': []
            }

    def export_all_stores(self, stores, max_workers=3):
        """并发导出所有店铺的待发货订单"""
        print(f"\\n🏭 开始并发处理 {len(stores)} 个店铺的待发货订单...")
        
        # 创建输出目录
        os.makedirs("多店铺待发货订单", exist_ok=True)
        
        all_results = []
        all_orders = []
        
        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_store = {
                executor.submit(self.export_store_orders, store): store 
                for store in stores
            }
            
            for future in as_completed(future_to_store):
                store = future_to_store[future]
                try:
                    result = future.result()
                    all_results.append(result)
                    
                    if result['success'] and 'orders' in result:
                        all_orders.extend(result['orders'])
                        
                except Exception as e:
                    print(f"❌ 处理店铺 {store['mall_name']} 时发生异常: {e}")
                    all_results.append({
                        'mall_id': store['mall_id'],
                        'mall_name': store['mall_name'],
                        'success': False,
                        'error': str(e),
                        'order_count': 0,
                        'files': []
                    })
        
        return all_results, all_orders

    def generate_summary_report(self, results, all_orders):
        """生成汇总报告"""
        print("\\n" + "=" * 80)
        print("🎉 多店铺待发货订单导出完成!")
        print("=" * 80)
        
        # 统计信息
        total_stores = len(results)
        success_stores = len([r for r in results if r['success']])
        failed_stores = total_stores - success_stores
        total_orders = sum(r['order_count'] for r in results if r['success'])
        
        print(f"📊 导出统计:")
        print(f"   处理店铺数: {total_stores}")
        print(f"   成功店铺数: {success_stores}")
        print(f"   失败店铺数: {failed_stores}")
        print(f"   待发货订单总数: {total_orders}")
        
        print(f"\\n📋 各店铺详情:")
        
        # 区域统计
        region_stats = {}
        for result in results:
            region = result.get('region_name', '未知区域')
            if region not in region_stats:
                region_stats[region] = {'stores': 0, 'orders': 0}
            region_stats[region]['stores'] += 1
            if result['success']:
                region_stats[region]['orders'] += result['order_count']
        
        print(f"\\n📊 区域统计:")
        for region, stats in region_stats.items():
            print(f"   🌍 {region}: {stats['stores']} 个店铺, {stats['orders']} 个订单")
        
        # 显示失败的店铺
        failed_results = [r for r in results if not r['success']]
        if failed_results:
            for result in failed_results:
                region = result.get('region_name', '未知区域')
                print(f"   ❌ {result['mall_name']} ({region}): 失败 - {result.get('error', '未知错误')}")
        
        print(f"\\n📁 输出目录: {os.path.abspath('多店铺待发货订单')}")
        print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    def run(self):
        """主运行函数"""
        print("🚀 多店铺Temu待发货订单导出工具 (修复版)")
        print("=" * 70)
        print("仅使用全球区，避免美国区权限问题")
        print("=" * 70)
        
        # 连接Chrome浏览器
        print("🌐 正在连接Chrome浏览器...")
        chrome_options = webdriver.ChromeOptions()
        chrome_options.debugger_address = "127.0.0.1:9337"
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            print("✅ 浏览器连接成功")
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            print("💡 请确保Chrome浏览器已启动调试模式:")
            print("   chrome.exe --remote-debugging-port=9337")
            return
        
        try:
            # 获取所有店铺
            stores = self.get_all_store_ids()
            
            if not stores:
                print("❌ 未发现任何可用店铺")
                return
            
            print(f"\\n📋 发现店铺列表 ({len(stores)}个):")
            for i, store in enumerate(stores, 1):
                print(f"   {i}. {store['mall_name']} (ID: {store['mall_id']})")
            
            # 确认开始处理
            confirm = input(f"\\n是否开始导出所有店铺的待发货订单？(y/n): ").strip().lower()
            if confirm != 'y':
                print("❌ 用户取消操作")
                return
            
            # 开始导出
            results, all_orders = self.export_all_stores(stores, max_workers=3)
            
            # 生成汇总报告
            self.generate_summary_report(results, all_orders)
            
        finally:
            # 询问是否关闭浏览器
            try:
                close_browser = input("\\n是否关闭浏览器？(y/n): ").strip().lower()
                if close_browser == 'y':
                    self.driver.quit()
                    print("🔒 浏览器已关闭")
            except:
                pass


def main():
    """主函数"""
    exporter = MultiStoreOrderExporter()
    exporter.run()


if __name__ == "__main__":
    main()
'''
    
    # 保存修复后的代码
    with open('多店铺待发货订单导出工具_修复版.py', 'w', encoding='utf-8') as f:
        f.write(fixed_code)
    
    print("✅ 已创建修复版本: 多店铺待发货订单导出工具_修复版.py")


def create_usage_guide():
    """创建使用指南"""
    
    guide = """# 修复版使用指南

## 主要修复

### 1. 变量作用域错误
- ✅ 修复了 `cannot access local variable 'region_name'` 错误
- ✅ 确保所有变量在正确的作用域内定义

### 2. 权限问题优化
- ✅ 只使用全球区 (agentseller.temu.com)，避免美国区403错误
- ✅ 移除了美国区的测试和访问，专注于有权限的区域

### 3. 代码结构优化
- ✅ 简化了错误处理逻辑
- ✅ 移除了复杂的重试机制
- ✅ 优化了变量定义位置

## 使用步骤

1. **启动Chrome调试模式**
   ```
   双击运行: 启动谷歌9229.bat
   ```

2. **登录Temu卖家后台**
   - 访问 https://seller.kuajingmaihuo.com
   - 完成登录

3. **运行修复版工具**
   ```
   python 多店铺待发货订单导出工具_修复版.py
   ```

## 预期结果

- ✅ 只处理全球区店铺，避免403错误
- ✅ 正常导出有待发货订单的店铺
- ✅ 跳过没有订单的店铺
- ✅ 生成详细的统计报告

## 注意事项

1. **只使用全球区**: 修复版只访问全球区，避免美国区权限问题
2. **权限要求**: 确保账号有全球区的订单访问权限
3. **网络稳定**: 保持网络连接稳定，避免中途断开

如果仍有问题，请检查：
- Chrome调试连接是否正常
- 登录状态是否有效
- 账号是否有订单管理权限
"""
    
    with open('修复版使用指南.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("✅ 已创建使用指南: 修复版使用指南.md")


def main():
    """主函数"""
    print("🔧 创建修复版多店铺导出工具")
    print("=" * 50)
    
    # 创建修复后的代码
    create_fixed_multi_store_exporter()
    
    # 创建使用指南
    create_usage_guide()
    
    print("\n🎯 修复内容:")
    print("1. ✅ 修复变量作用域错误")
    print("2. ✅ 只使用全球区，避免403错误")
    print("3. ✅ 简化错误处理逻辑")
    print("4. ✅ 优化代码结构")
    
    print("\n📁 生成的文件:")
    print("- 多店铺待发货订单导出工具_修复版.py")
    print("- 修复版使用指南.md")
    
    print("\n🚀 现在可以运行:")
    print("python 多店铺待发货订单导出工具_修复版.py")


if __name__ == "__main__":
    main()
