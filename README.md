# 多店铺待发货订单导出工具

## 项目简介

这是一个用于批量导出Temu多店铺待发货订单的自动化工具。支持同时处理多个店铺，自动生成Excel格式的订单报表。

**整合版特点**：
- ✅ 无外部依赖 - 所有功能已整合到单个文件
- ✅ 简化部署 - 只需要主程序和配置文件
- ✅ 易于维护 - 代码结构清晰，功能完整

## 文件结构

```
双区订单导出/
├── 多店铺待发货订单导出工具.py    # 主程序（整合版）
├── 订单数据导出工具.py            # 数据处理模块
├── config.json                   # 配置文件
├── 启动谷歌9229.bat              # Chrome调试模式启动脚本
├── 多店铺待发货订单/             # 输出目录
└── README.md                     # 项目说明文档
```

## 整合内容

主程序现已整合以下功能：
- **TemuOrderExporter类** - 单店铺订单导出核心功能
- **MultiStoreOrderExporter类** - 多店铺批量处理功能
- **订单数据提取和格式化** - 完整的数据处理流程
- **Excel文件生成** - 自动格式化和列宽调整

## 使用步骤

### 1. 启动Chrome调试模式
```bash
# 双击运行启动脚本
启动谷歌9229.bat

# 或手动启动
chrome.exe --remote-debugging-port=9337 --user-data-dir="C:\chrome_debug_data_temu"
```

### 2. 登录Temu卖家后台
在Chrome浏览器中访问：
- https://seller.kuajingmaihuo.com
- 完成登录流程

### 3. 运行导出工具
```bash
python 多店铺待发货订单导出工具.py
```

## 功能特点

- ✅ **多店铺支持**: 自动获取账号下所有店铺
- ✅ **双区域支持**: 支持美国区和全球区
- ✅ **并发处理**: 多线程同时处理多个店铺
- ✅ **Excel导出**: 自动生成格式化的Excel报表
- ✅ **详细统计**: 提供完整的导出统计信息
- ✅ **错误处理**: 智能处理各种异常情况
- ✅ **整合版本**: 无需外部依赖，单文件运行

## 输出文件

工具会在 `多店铺待发货订单/` 目录下生成以下文件：

- `店铺_[店铺名]_待发货订单_[时间戳].xlsx` - 各店铺的订单文件
- `汇总_所有店铺待发货订单_[时间戳].xlsx` - 所有店铺汇总文件

## 配置说明

### config.json
```json
{
    "说明": "手动配置认证参数 - 需要从浏览器中复制最新的cookies",
    "cookies": {
        "AccessToken": "请从浏览器中复制最新的AccessToken",
        "seller_temp": "请从浏览器中复制最新的seller_temp",
        "user_uin": "用户标识",
        "mallid": "店铺ID"
    }
}
```

## 系统要求

- Python 3.7+
- Chrome浏览器
- 必要的Python包：
  - selenium
  - requests
  - pandas
  - openpyxl

## 安装依赖

```bash
pip install selenium requests pandas openpyxl
```

## 注意事项

1. **Chrome调试模式**: 必须先启动Chrome调试模式
2. **登录状态**: 确保在Chrome中已登录Temu卖家后台
3. **网络稳定**: 保持网络连接稳定，避免中途断开
4. **权限要求**: 确保账号有订单管理权限

## 故障排除

### Chrome连接失败
```
❌ 连接浏览器失败
```
**解决方案**:
- 确保Chrome已启动调试模式
- 检查端口9337是否被占用
- 重新运行启动脚本

### 认证失败
```
❌ 认证失败，状态码: 403
```
**解决方案**:
- 检查登录状态
- 确认账号权限
- 重新登录Temu卖家后台

### 无权限访问
```
❌ No Permission to Access
```
**解决方案**:
- 使用主账号登录
- 联系管理员申请权限
- 检查店铺访问权限

## 更新日志

### v3.0 (当前版本 - 整合版)
- 整合TemuOrderExporter到主程序
- 移除外部文件依赖
- 简化部署和维护
- 优化代码结构

### v2.0
- 移除复杂的自动认证管理
- 简化错误处理逻辑
- 优化代码结构
- 提升稳定性

### v1.0
- 基础多店铺导出功能
- 双区域支持
- Excel文件生成

## 技术支持

如果遇到问题，请提供：
1. 完整的错误日志
2. Chrome版本信息
3. 账号权限级别
4. 操作系统信息

---

**注意**: 本工具仅用于合法的订单管理用途，请遵守Temu平台的使用条款。
